<?php

namespace Src\Domain\Admin\Services;

use App\Eloquent\Job;
use Src\Domain\Admin\Models\Job\JobDetail;
use Src\Domain\Admin\Models\User\UserDetail;

/**
 * Class JobApplicationService
 * @package Src\Domain\Admin\Services
 */
class JobApplicationService
{
    /**
     * Fetch job has applied
     * @param $date
     * @param $isMonth
     * @return array
     */
    public function fetchAll($date, $isMonth): array
    {
        $query = $this->jobQuery()
            ->with(['jobApplications'])
            ->where(function ($query) use ($date, $isMonth) {
                if ($date && $isMonth) {
                    $startOfMonth = date('Y-m-01', strtotime($date));
                    $endOfMonth = date('Y-m-t', strtotime($date));
                    $query->whereBetween('recruit_expired_at', [$startOfMonth, $endOfMonth]);
                } elseif ($date && !$isMonth) {
                    $query->whereDate('recruit_expired_at', $date);
                } elseif (!$date && !$isMonth) {
                    $startOfMonth = date('Y-m-01');
                    $endOfMonth = date('Y-m-t');
                    $query->whereBetween('recruit_expired_at', [$startOfMonth, $endOfMonth]);
                }
            })
            ->whereHas('jobApplications')
            ->orderBy('id', 'desc');

        $paginator = $query->paginate(PER_PAGE);

        $paginator->getCollection()->transform(function ($job) {
            return (new JobDetail($job))->toListComponent();
        });

        return pagination($paginator);
    }

    public function fetchApplier(int $job_id)
    {
        /** @var Job $job */
        $job = $this->jobQuery()->with('jobAppliers')->findOrFail($job_id);
        $appliers = $job->jobAppliers()->orderBy('id', 'desc');

        $paginator = $appliers->paginate(PER_PAGE);
        $paginator->getCollection()->transform(function ($item) {
            $job_approval = $item->pivot->approval_status;
            return (new UserDetail($item))->toApplierComponent($job_approval);
        });

        return pagination($paginator);
    }

    private function jobQuery()
    {
        return Job::queryModel();
    }
}
