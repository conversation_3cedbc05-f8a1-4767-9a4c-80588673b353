<script setup lang="ts">
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/vue/24/solid';
import FullCalendar from '@/components/common/shared/FullCalendar.vue';
import Button from '@/components/common/shared/Button.vue';
import { computed, ref } from 'vue';
import { router, usePage } from '@inertiajs/vue3';
import { useI18n } from '@/composables/useI18n.ts';
import { useRoute } from 'ziggy-js';
import { format, parseISO, isValid } from 'date-fns';
import { ListJobType } from '@/types/job.ts';

const props = defineProps<{
  jobs: ListJobType[];
}>();

const { t } = useI18n();
const route = useRoute();
const page = usePage();

const date =
  page.props.date && typeof page.props.date === 'string'
    ? isValid(parseISO(page.props.date))
      ? parseISO(page.props.date)
      : new Date()
    : new Date();
const isMonth = page.props.isMonth || false;
console.log(page.props.is_month);

const currentViewMonth = ref(date);
const isVisible = ref(true);

const events = computed(() => {
  return props.jobs.map(job => ({
    id: job.id,
    title: job.title,
    date: parseISO(job.recruitExpiredAt),
  }));
});

function toggleVisible() {
  isVisible.value = !isVisible.value;
}

const handleEventClick = payload => {
  console.log('event click', payload);
  router.get(route('admin.job-application.show', payload.event.id));
};

const getCurrentSearchParams = () => {
  const urlParams = new URLSearchParams(window.location.search);
  return {
    title: urlParams.get('title') || '',
    prefecture: urlParams.get('prefecture') || '',
    time_start: urlParams.get('time_start') || '',
    time_end: urlParams.get('time_end') || '',
    is_filled: urlParams.get('is_filled') || '',
  };
};

const handleDayClick = (payload: { date: Date; events: object }) => {
  const searchParams = getCurrentSearchParams();
  const params: any = {
    date: format(payload.date, 'yyyy-MM-dd'),
    ...searchParams,
  };

  Object.keys(params).forEach(key => {
    if (params[key] === '' || params[key] === null || params[key] === undefined) {
      delete params[key];
    }
  });

  router.get(route('admin.job-application.index'), params);
};

const handleMonthChange = async (payload: { month: Date }) => {
  currentViewMonth.value = payload.month;
  const searchParams = getCurrentSearchParams();
  const params: any = {
    date: format(payload.month, 'yyyy-MM-dd'),
    is_month: true,
    ...searchParams,
  };

  Object.keys(params).forEach(key => {
    if (params[key] === '' || params[key] === null || params[key] === undefined) {
      delete params[key];
    }
  });

  router.get(route('admin.job-application.index'), params);
};
</script>

<template>
  <div class="rounded-2xl border border-gray-200 bg-white mb-6">
    <div class="flex items-center justify-between p-4">
      <h4 class="text-lg font-semibold text-gray-800">
        {{ t('common.calendar.title') }}
      </h4>
      <Button variant="ghost" size="sm" class="text-gray-500" @click="toggleVisible">
        <ChevronUpIcon v-if="isVisible" class="w-5 h-5" />
        <ChevronDownIcon v-else class="w-5 h-5" />
      </Button>
    </div>
    <div class="custom-calendar" v-show="isVisible">
      <FullCalendar
        ref="calendarRef"
        :events="events"
        :initialMonth="currentViewMonth"
        :day-selected="currentViewMonth"
        @event-click="handleEventClick"
        @day-click="handleDayClick"
        @month-change="handleMonthChange"
      />
    </div>
  </div>
</template>

<style scoped></style>
