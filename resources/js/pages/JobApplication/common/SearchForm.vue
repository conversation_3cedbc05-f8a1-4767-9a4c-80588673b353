<script setup lang="ts">
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/vue/24/solid';
import DatePicker from '@/components/common/shared/DatePicker.vue';
import Button from '@/components/common/shared/Button.vue';
import VInput from '@/components/common/shared/VInput.vue';
import VSelect from '@/components/common/shared/VSelect.vue';
import { useI18n } from '@/composables/useI18n.ts';
import { computed, ref } from 'vue';
import { router, useForm } from '@inertiajs/vue3';
import { useRoute } from 'ziggy-js';

const { t } = useI18n();
const route = useRoute();

const isSearchVisible = ref(false);

const prefectureOptions = computed(() => [
  { label: t('common.field.all'), value: '' },
  ...(window as any).prefectureOptions,
]);

const isFilledOptions = computed(() => [
  { label: t('common.field.all'), value: '' },
  { label: t('models/job.is_filled.not_ended'), value: '0' },
  { label: t('models/job.is_filled.ended'), value: '1' },
]);

const currentParams = computed(() => {
  const urlParams = new URLSearchParams(window.location.search);
  return {
    date: urlParams.get('date') || '',
    is_month: urlParams.get('is_month') || '',
    title: urlParams.get('title') || '',
    prefecture: urlParams.get('prefecture') || '',
    time_start: urlParams.get('time_start') || '',
    time_end: urlParams.get('time_end') || '',
    is_filled: urlParams.get('is_filled') || '',
  };
});

const searchForm = useForm({
  title: currentParams.value.title,
  prefecture: currentParams.value.prefecture,
  time_start: currentParams.value.time_start,
  time_end: currentParams.value.time_end,
  is_filled: currentParams.value.is_filled,
});

function toggleSearchForm() {
  isSearchVisible.value = !isSearchVisible.value;
}

const handleSearch = () => {
  const searchParams: any = {
    ...searchForm.data(),
    ...(currentParams.value.date && { date: currentParams.value.date }),
    ...(currentParams.value.is_month && { is_month: currentParams.value.is_month }),
  };

  Object.keys(searchParams).forEach(key => {
    if (searchParams[key] === '' || searchParams[key] === null || searchParams[key] === undefined) {
      delete searchParams[key];
    }
  });

  router.get(route('admin.job-application.index'), searchParams, {
    preserveState: true,
    preserveScroll: true,
  });
};

const handleReset = () => {
  const preservedParams: any = {};
  if (currentParams.value.date) preservedParams.date = currentParams.value.date;
  if (currentParams.value.is_month) preservedParams.is_month = currentParams.value.is_month;

  router.get(route('admin.job-application.index'), preservedParams);
};
</script>

<template>
  <div class="p-4 mb-6 border border-gray-200 rounded-2xl">
    <div class="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
      <div class="w-full">
        <div class="flex items-center justify-between" :class="isSearchVisible ? 'mb-6' : ''">
          <h4 class="text-lg font-semibold text-gray-800">
            {{ t('common.field.search') }}
          </h4>
          <Button variant="ghost" size="sm" class="text-gray-500" @click="toggleSearchForm">
            <ChevronUpIcon v-if="isSearchVisible" class="w-5 h-5" />
            <ChevronDownIcon v-else class="w-5 h-5" />
          </Button>
        </div>
        <form @submit.prevent="handleSearch" v-show="isSearchVisible" class="transition-all duration-300">
          <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-7 2xl:gap-x-7">
            <v-input class="col-span-2" :label="t('models/job.field.title')" v-model="searchForm.title" />
            <div class="col-span-1">
              <label class="mb-1.5 block text-sm font-medium text-gray-700">{{
                t('models/job.searchField.time')
              }}</label>
              <div class="grid grid-cols-2 gap-4 items-center">
                <DatePicker type="time" v-model="searchForm.time_start" />
                <DatePicker type="time" v-model="searchForm.time_end" />
              </div>
            </div>
            <v-select
              :label="t('models/job.field.prefecture')"
              v-model="searchForm.prefecture"
              :options="prefectureOptions"
            />
            <v-select
              :label="t('models/job.field.is_filled')"
              v-model="searchForm.is_filled"
              :options="isFilledOptions"
            />
          </div>
          <div class="flex items-center justify-center gap-2 mt-6">
            <Button size="sm" variant="outline" @click="handleReset">
              {{ t('common.btn.reset') }}
            </Button>
            <Button size="sm" variant="primary" type="submit">
              {{ t('common.btn.search') }}
            </Button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
